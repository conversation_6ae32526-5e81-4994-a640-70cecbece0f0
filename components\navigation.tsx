"use client"

import { useState } from "react"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>u, X } from "lucide-react"

export default function Navigation() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  return (
    <header className="brutalist-border-thick border-b-0 border-l-0 border-r-0 bg-background sticky top-0 z-50">
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-between">
          <Link href="/" className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-accent brutalist-border brutalist-shadow-sm"></div>
            <h1 className="text-3xl font-serif font-bold text-foreground tracking-tight">mo.yo</h1>
          </Link>

          <nav className="hidden md:flex space-x-12">
            <Link
              href="/services"
              className="text-foreground hover:text-accent font-medium text-lg transition-colors uppercase tracking-wide"
            >
              SERVICES
            </Link>
            <Link
              href="/#about"
              className="text-foreground hover:text-accent font-medium text-lg transition-colors uppercase tracking-wide"
            >
              ABOUT
            </Link>
            <Link
              href="/#clients"
              className="text-foreground hover:text-accent font-medium text-lg transition-colors uppercase tracking-wide"
            >
              CLIENTS
            </Link>
            <Link
              href="/contact-us"
              className="text-foreground hover:text-accent font-medium text-lg transition-colors uppercase tracking-wide"
            >
              CONTACT
            </Link>
          </nav>

          <Button
            className="md:hidden bg-background brutalist-border brutalist-shadow-sm hover:bg-muted"
            variant="outline"
            size="lg"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          >
            {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
          </Button>
        </div>

        {/* Mobile menu */}
        {isMenuOpen && (
          <div className="md:hidden mt-8 p-6 bg-muted brutalist-border brutalist-shadow">
            <nav className="flex flex-col space-y-6">
              <Link
                href="/services"
                className="text-foreground hover:text-accent font-medium text-lg transition-colors uppercase tracking-wide"
                onClick={() => setIsMenuOpen(false)}
              >
                SERVICES
              </Link>
              <Link
                href="/#about"
                className="text-foreground hover:text-accent font-medium text-lg transition-colors uppercase tracking-wide"
                onClick={() => setIsMenuOpen(false)}
              >
                ABOUT
              </Link>
              <Link
                href="/#clients"
                className="text-foreground hover:text-accent font-medium text-lg transition-colors uppercase tracking-wide"
                onClick={() => setIsMenuOpen(false)}
              >
                CLIENTS
              </Link>
              <Link
                href="/contact-us"
                className="text-foreground hover:text-accent font-medium text-lg transition-colors uppercase tracking-wide"
                onClick={() => setIsMenuOpen(false)}
              >
                CONTACT
              </Link>
            </nav>
          </div>
        )}
      </div>
    </header>
  )
}
