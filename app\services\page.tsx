import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Code, Users, Bar<PERSON>hart3, <PERSON>lette, Cloud, ArrowRight, CheckCircle, Zap } from "lucide-react"
import Navigation from "@/components/navigation"
import Link from "next/link"

export default function ServicesPage() {
  return (
    <div className="min-h-screen bg-background">
      <Navigation />

      {/* Hero Section */}
      <section className="py-24 px-4 relative overflow-hidden">
        <div className="container mx-auto">
          <div className="max-w-5xl">
            <div className="mb-8">
              <div className="inline-block bg-accent text-accent-foreground px-6 py-3 brutalist-border brutalist-shadow-sm mb-6">
                <span className="font-serif font-bold text-lg uppercase tracking-wide">OUR EXPERTISE</span>
              </div>
            </div>
            <h1 className="text-7xl md:text-9xl font-serif font-bold text-foreground leading-none mb-8 tracking-tight">
              SERVICES
              <br />
              <span className="text-accent">THAT</span>
              <br />
              <span className="relative">
                DELIVER
                <div className="absolute -bottom-2 left-0 w-full h-2 bg-accent"></div>
              </span>
            </h1>
            <p className="text-2xl text-foreground mb-12 max-w-3xl font-medium leading-relaxed">
              From custom software to cloud solutions, we provide comprehensive tech services with
              <span className="text-accent font-bold"> AFRICAN INNOVATION</span> and global standards.
            </p>
          </div>
        </div>
        <div className="absolute top-20 right-10 w-20 h-20 bg-foreground brutalist-border rotate-12 hidden lg:block"></div>
        <div className="absolute bottom-20 right-32 w-16 h-16 bg-accent brutalist-border -rotate-12 hidden lg:block"></div>
      </section>

      {/* Services Grid */}
      <section className="py-24 px-4 bg-muted">
        <div className="container mx-auto">
          <div className="grid gap-16">
            {/* Custom Software */}
            <Card className="p-12 brutalist-border brutalist-shadow-lg bg-background hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-200">
              <div className="grid md:grid-cols-2 gap-12 items-start">
                <div>
                  <Code className="h-20 w-20 text-accent mb-8" />
                  <h2 className="text-5xl font-serif font-bold text-foreground mb-6 uppercase tracking-tight">
                    CUSTOM SOFTWARE
                  </h2>
                  <p className="text-xl text-foreground mb-8 leading-relaxed">
                    We build enterprise applications, digital platforms, and mobile apps that scale with your business.
                    <span className="text-accent font-bold"> NO COMPROMISES.</span>
                  </p>
                  <div className="space-y-4 mb-8">
                    <div className="flex items-center gap-4">
                      <CheckCircle className="h-6 w-6 text-accent" />
                      <span className="text-foreground font-medium text-lg">Enterprise Web Applications</span>
                    </div>
                    <div className="flex items-center gap-4">
                      <CheckCircle className="h-6 w-6 text-accent" />
                      <span className="text-foreground font-medium text-lg">Mobile App Development</span>
                    </div>
                    <div className="flex items-center gap-4">
                      <CheckCircle className="h-6 w-6 text-accent" />
                      <span className="text-foreground font-medium text-lg">API Development & Integration</span>
                    </div>
                    <div className="flex items-center gap-4">
                      <CheckCircle className="h-6 w-6 text-accent" />
                      <span className="text-foreground font-medium text-lg">Legacy System Modernization</span>
                    </div>
                  </div>
                  <Link href="/contact-us">
                    <Button className="bg-accent hover:bg-accent/90 text-accent-foreground brutalist-border brutalist-shadow font-bold text-lg px-8 py-4 uppercase tracking-wide">
                      START PROJECT
                      <ArrowRight className="ml-2 h-5 w-5" />
                    </Button>
                  </Link>
                </div>
                <div className="grid grid-cols-2 gap-6">
                  <div className="bg-muted p-6 brutalist-border">
                    <div className="text-3xl font-serif font-bold text-accent mb-2">React</div>
                    <div className="text-foreground font-medium">Frontend Framework</div>
                  </div>
                  <div className="bg-muted p-6 brutalist-border">
                    <div className="text-3xl font-serif font-bold text-accent mb-2">Node.js</div>
                    <div className="text-foreground font-medium">Backend Runtime</div>
                  </div>
                  <div className="bg-muted p-6 brutalist-border">
                    <div className="text-3xl font-serif font-bold text-accent mb-2">Python</div>
                    <div className="text-foreground font-medium">Data & AI</div>
                  </div>
                  <div className="bg-muted p-6 brutalist-border">
                    <div className="text-3xl font-serif font-bold text-accent mb-2">AWS</div>
                    <div className="text-foreground font-medium">Cloud Platform</div>
                  </div>
                </div>
              </div>
            </Card>

            {/* Co-Sourcing */}
            <Card className="p-12 brutalist-border brutalist-shadow-lg bg-background hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-200">
              <div className="grid md:grid-cols-2 gap-12 items-start">
                <div>
                  <Users className="h-20 w-20 text-accent mb-8" />
                  <h2 className="text-5xl font-serif font-bold text-foreground mb-6 uppercase tracking-tight">
                    CO-SOURCING
                  </h2>
                  <p className="text-xl text-foreground mb-8 leading-relaxed">
                    Access top African tech talent and dedicated teams that integrate seamlessly with your operations.
                    <span className="text-accent font-bold"> WORLD-CLASS TALENT.</span>
                  </p>
                  <div className="space-y-4 mb-8">
                    <div className="flex items-center gap-4">
                      <CheckCircle className="h-6 w-6 text-accent" />
                      <span className="text-foreground font-medium text-lg">Senior Developers & Architects</span>
                    </div>
                    <div className="flex items-center gap-4">
                      <CheckCircle className="h-6 w-6 text-accent" />
                      <span className="text-foreground font-medium text-lg">UI/UX Designers</span>
                    </div>
                    <div className="flex items-center gap-4">
                      <CheckCircle className="h-6 w-6 text-accent" />
                      <span className="text-foreground font-medium text-lg">Dedicated Development Teams</span>
                    </div>
                    <div className="flex items-center gap-4">
                      <CheckCircle className="h-6 w-6 text-accent" />
                      <span className="text-foreground font-medium text-lg">Project Management</span>
                    </div>
                  </div>
                  <Link href="/contact-us">
                    <Button className="bg-accent hover:bg-accent/90 text-accent-foreground brutalist-border brutalist-shadow font-bold text-lg px-8 py-4 uppercase tracking-wide">
                      HIRE TALENT
                      <ArrowRight className="ml-2 h-5 w-5" />
                    </Button>
                  </Link>
                </div>
                <div className="space-y-6">
                  <div className="bg-accent text-accent-foreground p-8 brutalist-border text-center">
                    <div className="text-4xl font-serif font-bold mb-2">50+</div>
                    <div className="font-bold uppercase tracking-wide">DEVELOPERS</div>
                  </div>
                  <div className="bg-accent text-accent-foreground p-8 brutalist-border text-center">
                    <div className="text-4xl font-serif font-bold mb-2">15+</div>
                    <div className="font-bold uppercase tracking-wide">DESIGNERS</div>
                  </div>
                  <div className="bg-accent text-accent-foreground p-8 brutalist-border text-center">
                    <div className="text-4xl font-serif font-bold mb-2">24/7</div>
                    <div className="font-bold uppercase tracking-wide">SUPPORT</div>
                  </div>
                </div>
              </div>
            </Card>

            {/* Data & Analytics */}
            <Card className="p-12 brutalist-border brutalist-shadow-lg bg-background hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-200">
              <div className="grid md:grid-cols-2 gap-12 items-start">
                <div>
                  <BarChart3 className="h-20 w-20 text-accent mb-8" />
                  <h2 className="text-5xl font-serif font-bold text-foreground mb-6 uppercase tracking-tight">
                    DATA & ANALYTICS
                  </h2>
                  <p className="text-xl text-foreground mb-8 leading-relaxed">
                    Transform your data into actionable insights with our comprehensive analytics and machine learning
                    solutions.
                    <span className="text-accent font-bold"> DATA-DRIVEN DECISIONS.</span>
                  </p>
                  <div className="space-y-4 mb-8">
                    <div className="flex items-center gap-4">
                      <CheckCircle className="h-6 w-6 text-accent" />
                      <span className="text-foreground font-medium text-lg">Business Intelligence Dashboards</span>
                    </div>
                    <div className="flex items-center gap-4">
                      <CheckCircle className="h-6 w-6 text-accent" />
                      <span className="text-foreground font-medium text-lg">Machine Learning Models</span>
                    </div>
                    <div className="flex items-center gap-4">
                      <CheckCircle className="h-6 w-6 text-accent" />
                      <span className="text-foreground font-medium text-lg">Data Pipeline Engineering</span>
                    </div>
                    <div className="flex items-center gap-4">
                      <CheckCircle className="h-6 w-6 text-accent" />
                      <span className="text-foreground font-medium text-lg">Predictive Analytics</span>
                    </div>
                  </div>
                  <Link href="/contact-us">
                    <Button className="bg-accent hover:bg-accent/90 text-accent-foreground brutalist-border brutalist-shadow font-bold text-lg px-8 py-4 uppercase tracking-wide">
                      ANALYZE DATA
                      <ArrowRight className="ml-2 h-5 w-5" />
                    </Button>
                  </Link>
                </div>
                <div className="grid grid-cols-3 gap-4">
                  <div className="bg-muted p-4 brutalist-border text-center">
                    <div className="font-bold text-foreground text-lg">PYTHON</div>
                  </div>
                  <div className="bg-muted p-4 brutalist-border text-center">
                    <div className="font-bold text-foreground text-lg">R</div>
                  </div>
                  <div className="bg-muted p-4 brutalist-border text-center">
                    <div className="font-bold text-foreground text-lg">SQL</div>
                  </div>
                  <div className="bg-muted p-4 brutalist-border text-center">
                    <div className="font-bold text-foreground text-lg">TABLEAU</div>
                  </div>
                  <div className="bg-muted p-4 brutalist-border text-center">
                    <div className="font-bold text-foreground text-lg">POWER BI</div>
                  </div>
                  <div className="bg-muted p-4 brutalist-border text-center">
                    <div className="font-bold text-foreground text-lg">SPARK</div>
                  </div>
                </div>
              </div>
            </Card>

            {/* Product Design */}
            <Card className="p-12 brutalist-border brutalist-shadow-lg bg-background hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-200">
              <div className="grid md:grid-cols-2 gap-12 items-start">
                <div>
                  <Palette className="h-20 w-20 text-accent mb-8" />
                  <h2 className="text-5xl font-serif font-bold text-foreground mb-6 uppercase tracking-tight">
                    PRODUCT DESIGN
                  </h2>
                  <p className="text-xl text-foreground mb-8 leading-relaxed">
                    User-centered design that combines strategy, creativity, and functionality to create exceptional
                    digital experiences.
                    <span className="text-accent font-bold"> DESIGN THAT WORKS.</span>
                  </p>
                  <div className="space-y-4 mb-8">
                    <div className="flex items-center gap-4">
                      <CheckCircle className="h-6 w-6 text-accent" />
                      <span className="text-foreground font-medium text-lg">Product Strategy & Research</span>
                    </div>
                    <div className="flex items-center gap-4">
                      <CheckCircle className="h-6 w-6 text-accent" />
                      <span className="text-foreground font-medium text-lg">UI/UX Design</span>
                    </div>
                    <div className="flex items-center gap-4">
                      <CheckCircle className="h-6 w-6 text-accent" />
                      <span className="text-foreground font-medium text-lg">Design Systems</span>
                    </div>
                    <div className="flex items-center gap-4">
                      <CheckCircle className="h-6 w-6 text-accent" />
                      <span className="text-foreground font-medium text-lg">Prototyping & Testing</span>
                    </div>
                  </div>
                  <Link href="/contact-us">
                    <Button className="bg-accent hover:bg-accent/90 text-accent-foreground brutalist-border brutalist-shadow font-bold text-lg px-8 py-4 uppercase tracking-wide">
                      DESIGN PRODUCT
                      <ArrowRight className="ml-2 h-5 w-5" />
                    </Button>
                  </Link>
                </div>
                <div className="space-y-6">
                  <div className="bg-muted p-6 brutalist-border">
                    <h4 className="font-serif font-bold text-foreground text-xl mb-3 uppercase">RESEARCH</h4>
                    <p className="text-foreground">User interviews, market analysis, competitive research</p>
                  </div>
                  <div className="bg-muted p-6 brutalist-border">
                    <h4 className="font-serif font-bold text-foreground text-xl mb-3 uppercase">DESIGN</h4>
                    <p className="text-foreground">Wireframes, prototypes, visual design, design systems</p>
                  </div>
                  <div className="bg-muted p-6 brutalist-border">
                    <h4 className="font-serif font-bold text-foreground text-xl mb-3 uppercase">TEST</h4>
                    <p className="text-foreground">Usability testing, A/B testing, iteration</p>
                  </div>
                </div>
              </div>
            </Card>

            {/* Cloud Services */}
            <Card className="p-12 brutalist-border brutalist-shadow-lg bg-background hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-200">
              <div className="grid md:grid-cols-2 gap-12 items-start">
                <div>
                  <Cloud className="h-20 w-20 text-accent mb-8" />
                  <h2 className="text-5xl font-serif font-bold text-foreground mb-6 uppercase tracking-tight">
                    CLOUD SERVICES
                  </h2>
                  <p className="text-xl text-foreground mb-8 leading-relaxed">
                    Complete cloud transformation journey from assessment to deployment, ensuring scalability and
                    security.
                    <span className="text-accent font-bold"> CLOUD-FIRST APPROACH.</span>
                  </p>
                  <div className="space-y-4 mb-8">
                    <div className="flex items-center gap-4">
                      <CheckCircle className="h-6 w-6 text-accent" />
                      <span className="text-foreground font-medium text-lg">Cloud Architecture Design</span>
                    </div>
                    <div className="flex items-center gap-4">
                      <CheckCircle className="h-6 w-6 text-accent" />
                      <span className="text-foreground font-medium text-lg">Migration Services</span>
                    </div>
                    <div className="flex items-center gap-4">
                      <CheckCircle className="h-6 w-6 text-accent" />
                      <span className="text-foreground font-medium text-lg">DevOps & CI/CD</span>
                    </div>
                    <div className="flex items-center gap-4">
                      <CheckCircle className="h-6 w-6 text-accent" />
                      <span className="text-foreground font-medium text-lg">24/7 Monitoring & Support</span>
                    </div>
                  </div>
                  <Link href="/contact-us">
                    <Button className="bg-accent hover:bg-accent/90 text-accent-foreground brutalist-border brutalist-shadow font-bold text-lg px-8 py-4 uppercase tracking-wide">
                      GO CLOUD
                      <ArrowRight className="ml-2 h-5 w-5" />
                    </Button>
                  </Link>
                </div>
                <div className="grid grid-cols-2 gap-6">
                  <div className="bg-muted p-6 brutalist-border text-center">
                    <div className="text-2xl font-serif font-bold text-accent mb-2">ASSESS</div>
                    <div className="text-foreground font-medium text-sm">Current infrastructure analysis</div>
                  </div>
                  <div className="bg-muted p-6 brutalist-border text-center">
                    <div className="text-2xl font-serif font-bold text-accent mb-2">DESIGN</div>
                    <div className="text-foreground font-medium text-sm">Cloud architecture planning</div>
                  </div>
                  <div className="bg-muted p-6 brutalist-border text-center">
                    <div className="text-2xl font-serif font-bold text-accent mb-2">BUILD</div>
                    <div className="text-foreground font-medium text-sm">Implementation & deployment</div>
                  </div>
                  <div className="bg-muted p-6 brutalist-border text-center">
                    <div className="text-2xl font-serif font-bold text-accent mb-2">SUPPORT</div>
                    <div className="text-foreground font-medium text-sm">Ongoing maintenance & optimization</div>
                  </div>
                </div>
              </div>
            </Card>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-24 px-4 relative overflow-hidden">
        <div className="container mx-auto text-center">
          <div className="mb-8">
            <div className="inline-block bg-foreground text-background px-6 py-3 brutalist-border brutalist-shadow-sm mb-6">
              <span className="font-serif font-bold text-lg uppercase tracking-wide">READY TO START?</span>
            </div>
          </div>
          <h3 className="text-6xl font-serif font-bold text-foreground mb-8 uppercase tracking-tight">
            LET'S BUILD SOMETHING
            <br />
            <span className="text-accent">EXTRAORDINARY</span>
          </h3>
          <p className="text-2xl text-foreground mb-12 max-w-4xl mx-auto leading-relaxed font-medium">
            Choose mo.yo for your next project and experience the power of
            <span className="text-accent font-bold"> AFRICAN INNOVATION</span> combined with world-class execution.
          </p>
          <Link href="/contact-us">
            <Button
              size="lg"
              className="bg-accent hover:bg-accent/90 text-accent-foreground brutalist-border brutalist-shadow font-bold text-xl px-12 py-8 uppercase tracking-wide"
            >
              START YOUR PROJECT
              <Zap className="ml-3 h-6 w-6" />
            </Button>
          </Link>
        </div>
        <div className="absolute top-10 left-10 w-24 h-24 bg-accent brutalist-border rotate-45 hidden lg:block"></div>
        <div className="absolute bottom-10 right-10 w-20 h-20 bg-foreground brutalist-border -rotate-12 hidden lg:block"></div>
      </section>

      {/* Footer */}
      <footer className="brutalist-border-thick border-t-0 border-l-0 border-r-0 bg-muted py-16 px-4">
        <div className="container mx-auto">
          <div className="grid md:grid-cols-4 gap-12">
            <div>
              <Link href="/" className="flex items-center space-x-3 mb-6">
                <div className="w-10 h-10 bg-accent brutalist-border brutalist-shadow-sm"></div>
                <h4 className="text-2xl font-serif font-bold text-foreground">mo.yo</h4>
              </Link>
              <p className="text-foreground font-medium leading-relaxed">
                African software development company serving clients globally with
                <span className="text-accent font-bold"> UNCOMPROMISING QUALITY.</span>
              </p>
            </div>
            <div>
              <h5 className="font-serif font-bold text-foreground mb-6 text-xl uppercase tracking-wide">SERVICES</h5>
              <ul className="space-y-3 text-foreground font-medium">
                <li className="hover:text-accent transition-colors cursor-pointer">CUSTOM SOFTWARE</li>
                <li className="hover:text-accent transition-colors cursor-pointer">CO-SOURCING</li>
                <li className="hover:text-accent transition-colors cursor-pointer">DATA & ANALYTICS</li>
                <li className="hover:text-accent transition-colors cursor-pointer">PRODUCT DESIGN</li>
                <li className="hover:text-accent transition-colors cursor-pointer">CLOUD SERVICES</li>
              </ul>
            </div>
            <div>
              <h5 className="font-serif font-bold text-foreground mb-6 text-xl uppercase tracking-wide">COMPANY</h5>
              <ul className="space-y-3 text-foreground font-medium">
                <li className="hover:text-accent transition-colors cursor-pointer">ABOUT US</li>
                <li className="hover:text-accent transition-colors cursor-pointer">OUR WORK</li>
                <li className="hover:text-accent transition-colors cursor-pointer">CAREERS</li>
                <li className="hover:text-accent transition-colors cursor-pointer">CONTACT</li>
              </ul>
            </div>
            <div>
              <h5 className="font-serif font-bold text-foreground mb-6 text-xl uppercase tracking-wide">CONNECT</h5>
              <div className="space-y-4">
                <div className="bg-background p-4 brutalist-border">
                  <p className="text-foreground font-bold"><EMAIL></p>
                </div>
                <div className="bg-background p-4 brutalist-border">
                  <p className="text-foreground font-bold">wearemoyo.dev</p>
                </div>
              </div>
            </div>
          </div>
          <div className="brutalist-border-thick border-t-0 border-l-0 border-r-0 mt-12 pt-8 text-center">
            <p className="text-foreground font-bold text-lg uppercase tracking-wide">
              © 2024 mo.yo. BUILT WITH <span className="text-accent">AFRICAN INNOVATION.</span>
            </p>
          </div>
        </div>
      </footer>
    </div>
  )
}
