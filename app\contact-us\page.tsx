"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Mail, Phone, MapPin, Send, Zap, Globe, Clock } from "lucide-react"
import Navigation from "@/components/navigation"

export default function ContactPage() {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    company: "",
    service: "",
    message: "",
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Handle form submission
    console.log("Form submitted:", formData)
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    })
  }

  return (
    <div className="min-h-screen bg-background">
      <Navigation />

      {/* Hero Section */}
      <section className="py-24 px-4 relative overflow-hidden">
        <div className="container mx-auto">
          <div className="max-w-5xl">
            <div className="mb-8">
              <div className="inline-block bg-accent text-accent-foreground px-6 py-3 brutalist-border brutalist-shadow-sm mb-6">
                <span className="font-serif font-bold text-lg uppercase tracking-wide">GET IN TOUCH</span>
              </div>
            </div>
            <h1 className="text-7xl md:text-9xl font-serif font-bold text-foreground leading-none mb-8 tracking-tight">
              LET'S
              <br />
              <span className="text-accent">TALK</span>
              <br />
              <span className="relative">
                BUSINESS
                <div className="absolute -bottom-2 left-0 w-full h-2 bg-accent"></div>
              </span>
            </h1>
            <p className="text-2xl text-foreground mb-12 max-w-3xl font-medium leading-relaxed">
              Ready to transform your ideas into reality? Contact mo.yo today and let's build something
              <span className="text-accent font-bold"> EXTRAORDINARY</span> together.
            </p>
          </div>
        </div>
        <div className="absolute top-20 right-10 w-20 h-20 bg-foreground brutalist-border rotate-12 hidden lg:block"></div>
        <div className="absolute bottom-20 right-32 w-16 h-16 bg-accent brutalist-border -rotate-12 hidden lg:block"></div>
      </section>

      {/* Contact Form & Info */}
      <section className="py-24 px-4 bg-muted">
        <div className="container mx-auto">
          <div className="grid lg:grid-cols-2 gap-16">
            {/* Contact Form */}
            <Card className="p-12 brutalist-border brutalist-shadow-lg bg-background">
              <h2 className="text-4xl font-serif font-bold text-foreground mb-8 uppercase tracking-tight">
                START YOUR PROJECT
              </h2>
              <form onSubmit={handleSubmit} className="space-y-8">
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-foreground font-bold text-lg mb-3 uppercase tracking-wide">
                      NAME *
                    </label>
                    <Input
                      type="text"
                      name="name"
                      value={formData.name}
                      onChange={handleChange}
                      required
                      className="brutalist-border bg-background text-foreground text-lg p-4 h-auto"
                      placeholder="YOUR NAME"
                    />
                  </div>
                  <div>
                    <label className="block text-foreground font-bold text-lg mb-3 uppercase tracking-wide">
                      EMAIL *
                    </label>
                    <Input
                      type="email"
                      name="email"
                      value={formData.email}
                      onChange={handleChange}
                      required
                      className="brutalist-border bg-background text-foreground text-lg p-4 h-auto"
                      placeholder="<EMAIL>"
                    />
                  </div>
                </div>

                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-foreground font-bold text-lg mb-3 uppercase tracking-wide">
                      COMPANY
                    </label>
                    <Input
                      type="text"
                      name="company"
                      value={formData.company}
                      onChange={handleChange}
                      className="brutalist-border bg-background text-foreground text-lg p-4 h-auto"
                      placeholder="COMPANY NAME"
                    />
                  </div>
                  <div>
                    <label className="block text-foreground font-bold text-lg mb-3 uppercase tracking-wide">
                      SERVICE NEEDED
                    </label>
                    <select
                      name="service"
                      value={formData.service}
                      onChange={handleChange}
                      className="w-full brutalist-border bg-background text-foreground text-lg p-4 h-auto"
                    >
                      <option value="">SELECT SERVICE</option>
                      <option value="custom-software">CUSTOM SOFTWARE</option>
                      <option value="co-sourcing">CO-SOURCING</option>
                      <option value="data-analytics">DATA & ANALYTICS</option>
                      <option value="product-design">PRODUCT DESIGN</option>
                      <option value="cloud-services">CLOUD SERVICES</option>
                      <option value="consultation">CONSULTATION</option>
                    </select>
                  </div>
                </div>

                <div>
                  <label className="block text-foreground font-bold text-lg mb-3 uppercase tracking-wide">
                    PROJECT DETAILS *
                  </label>
                  <Textarea
                    name="message"
                    value={formData.message}
                    onChange={handleChange}
                    required
                    rows={6}
                    className="brutalist-border bg-background text-foreground text-lg p-4"
                    placeholder="TELL US ABOUT YOUR PROJECT, TIMELINE, AND REQUIREMENTS..."
                  />
                </div>

                <Button
                  type="submit"
                  size="lg"
                  className="w-full bg-accent hover:bg-accent/90 text-accent-foreground brutalist-border brutalist-shadow font-bold text-xl py-6 uppercase tracking-wide"
                >
                  SEND MESSAGE
                  <Send className="ml-3 h-6 w-6" />
                </Button>
              </form>
            </Card>

            {/* Contact Information */}
            <div className="space-y-8">
              <Card className="p-10 brutalist-border brutalist-shadow bg-background hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-200">
                <Mail className="h-16 w-16 text-accent mb-6" />
                <h3 className="text-3xl font-serif font-bold text-foreground mb-4 uppercase tracking-tight">
                  EMAIL US
                </h3>
                <p className="text-foreground text-lg mb-6 leading-relaxed">
                  Get in touch via email for detailed project discussions and proposals.
                </p>
                <div className="bg-muted p-6 brutalist-border">
                  <p className="text-foreground font-bold text-xl"><EMAIL></p>
                </div>
              </Card>

              <Card className="p-10 brutalist-border brutalist-shadow bg-background hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-200">
                <Phone className="h-16 w-16 text-accent mb-6" />
                <h3 className="text-3xl font-serif font-bold text-foreground mb-4 uppercase tracking-tight">CALL US</h3>
                <p className="text-foreground text-lg mb-6 leading-relaxed">
                  Schedule a call to discuss your project requirements and timeline.
                </p>
                <div className="bg-muted p-6 brutalist-border">
                  <p className="text-foreground font-bold text-xl">+27 (0) 11 123 4567</p>
                </div>
              </Card>

              <Card className="p-10 brutalist-border brutalist-shadow bg-background hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-200">
                <MapPin className="h-16 w-16 text-accent mb-6" />
                <h3 className="text-3xl font-serif font-bold text-foreground mb-4 uppercase tracking-tight">
                  VISIT US
                </h3>
                <p className="text-foreground text-lg mb-6 leading-relaxed">
                  Based in South Africa, serving clients across Africa and globally.
                </p>
                <div className="bg-muted p-6 brutalist-border">
                  <p className="text-foreground font-bold text-lg">
                    JOHANNESBURG, SOUTH AFRICA
                    <br />
                    CAPE TOWN, SOUTH AFRICA
                  </p>
                </div>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Quick Stats */}
      <section className="py-24 px-4">
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <h3 className="text-5xl font-serif font-bold text-foreground mb-6 uppercase tracking-tight">
              WHY CHOOSE <span className="text-accent">MO.YO</span>
            </h3>
            <div className="w-32 h-2 bg-accent mx-auto"></div>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <Card className="p-10 brutalist-border brutalist-shadow bg-muted hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-200 text-center">
              <Zap className="h-16 w-16 text-accent mx-auto mb-6" />
              <div className="text-5xl font-serif font-bold text-accent mb-4">48H</div>
              <h4 className="text-xl font-serif font-bold text-foreground mb-3 uppercase tracking-wide">
                RESPONSE TIME
              </h4>
              <p className="text-foreground leading-relaxed">
                We respond to all inquiries within 48 hours with detailed project assessments.
              </p>
            </Card>

            <Card className="p-10 brutalist-border brutalist-shadow bg-muted hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-200 text-center">
              <Globe className="h-16 w-16 text-accent mx-auto mb-6" />
              <div className="text-5xl font-serif font-bold text-accent mb-4">15+</div>
              <h4 className="text-xl font-serif font-bold text-foreground mb-3 uppercase tracking-wide">
                COUNTRIES SERVED
              </h4>
              <p className="text-foreground leading-relaxed">
                Global reach with local African insight and international standards.
              </p>
            </Card>

            <Card className="p-10 brutalist-border brutalist-shadow bg-muted hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-200 text-center">
              <Clock className="h-16 w-16 text-accent mx-auto mb-6" />
              <div className="text-5xl font-serif font-bold text-accent mb-4">24/7</div>
              <h4 className="text-xl font-serif font-bold text-foreground mb-3 uppercase tracking-wide">
                SUPPORT AVAILABLE
              </h4>
              <p className="text-foreground leading-relaxed">
                Round-the-clock support for all our clients across different time zones.
              </p>
            </Card>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="brutalist-border-thick border-t-0 border-l-0 border-r-0 bg-muted py-16 px-4">
        <div className="container mx-auto">
          <div className="grid md:grid-cols-4 gap-12">
            <div>
              <div className="flex items-center space-x-3 mb-6">
                <div className="w-10 h-10 bg-accent brutalist-border brutalist-shadow-sm"></div>
                <h4 className="text-2xl font-serif font-bold text-foreground">mo.yo</h4>
              </div>
              <p className="text-foreground font-medium leading-relaxed">
                African software development company serving clients globally with
                <span className="text-accent font-bold"> UNCOMPROMISING QUALITY.</span>
              </p>
            </div>
            <div>
              <h5 className="font-serif font-bold text-foreground mb-6 text-xl uppercase tracking-wide">SERVICES</h5>
              <ul className="space-y-3 text-foreground font-medium">
                <li className="hover:text-accent transition-colors cursor-pointer">CUSTOM SOFTWARE</li>
                <li className="hover:text-accent transition-colors cursor-pointer">CO-SOURCING</li>
                <li className="hover:text-accent transition-colors cursor-pointer">DATA & ANALYTICS</li>
                <li className="hover:text-accent transition-colors cursor-pointer">PRODUCT DESIGN</li>
                <li className="hover:text-accent transition-colors cursor-pointer">CLOUD SERVICES</li>
              </ul>
            </div>
            <div>
              <h5 className="font-serif font-bold text-foreground mb-6 text-xl uppercase tracking-wide">COMPANY</h5>
              <ul className="space-y-3 text-foreground font-medium">
                <li className="hover:text-accent transition-colors cursor-pointer">ABOUT US</li>
                <li className="hover:text-accent transition-colors cursor-pointer">OUR WORK</li>
                <li className="hover:text-accent transition-colors cursor-pointer">CAREERS</li>
                <li className="hover:text-accent transition-colors cursor-pointer">CONTACT</li>
              </ul>
            </div>
            <div>
              <h5 className="font-serif font-bold text-foreground mb-6 text-xl uppercase tracking-wide">CONNECT</h5>
              <div className="space-y-4">
                <div className="bg-background p-4 brutalist-border">
                  <p className="text-foreground font-bold"><EMAIL></p>
                </div>
                <div className="bg-background p-4 brutalist-border">
                  <p className="text-foreground font-bold">wearemoyo.dev</p>
                </div>
              </div>
            </div>
          </div>
          <div className="brutalist-border-thick border-t-0 border-l-0 border-r-0 mt-12 pt-8 text-center">
            <p className="text-foreground font-bold text-lg uppercase tracking-wide">
              © 2024 mo.yo. BUILT WITH <span className="text-accent">AFRICAN INNOVATION.</span>
            </p>
          </div>
        </div>
      </footer>
    </div>
  )
}
