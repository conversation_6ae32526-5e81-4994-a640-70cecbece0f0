@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
  /* Pure neobrutalist color system - stark white primary with high contrast blacks */
  --background: #ffffff; /* Pure white background */
  --foreground: #000000; /* Pure black text for maximum contrast */
  --card: #ffffff; /* White cards */
  --card-foreground: #000000;
  --popover: #ffffff;
  --popover-foreground: #000000;
  --primary: #000000; /* Black primary for stark contrast */
  --primary-foreground: #ffffff;
  --secondary: #f5f5f5; /* Very light gray */
  --secondary-foreground: #000000;
  --muted: #f8f8f8; /* Subtle off-white */
  --muted-foreground: #404040;
  --accent: #ff0000; /* Bold red accent for neobrutalist impact */
  --accent-foreground: #ffffff;
  --destructive: #ff0000;
  --destructive-foreground: #ffffff;
  --border: #000000; /* Pure black borders for stark contrast */
  --input: #ffffff;
  --ring: #ff0000;
  --chart-1: #ff0000;
  --chart-2: #000000;
  --chart-3: #808080;
  --chart-4: #404040;
  --chart-5: #c0c0c0;
  --radius: 0px; /* No border radius for pure neobrutalist aesthetic */
  --sidebar: #ffffff;
  --sidebar-foreground: #000000;
  --sidebar-primary: #000000;
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: #f8f8f8;
  --sidebar-accent-foreground: #000000;
  --sidebar-border: #000000;
  --sidebar-ring: #ff0000;
}

.dark {
  --background: #000000;
  --foreground: #ffffff;
  --card: #000000;
  --card-foreground: #ffffff;
  --popover: #000000;
  --popover-foreground: #ffffff;
  --primary: #ffffff;
  --primary-foreground: #000000;
  --secondary: #1a1a1a;
  --secondary-foreground: #ffffff;
  --muted: #0a0a0a;
  --muted-foreground: #a0a0a0;
  --accent: #ff0000;
  --accent-foreground: #ffffff;
  --destructive: #ff0000;
  --destructive-foreground: #ffffff;
  --border: #ffffff;
  --input: #000000;
  --ring: #ff0000;
  --chart-1: #ff0000;
  --chart-2: #ffffff;
  --chart-3: #808080;
  --chart-4: #c0c0c0;
  --chart-5: #404040;
  --sidebar: #000000;
  --sidebar-foreground: #ffffff;
  --sidebar-primary: #ffffff;
  --sidebar-primary-foreground: #000000;
  --sidebar-accent: #0a0a0a;
  --sidebar-accent-foreground: #ffffff;
  --sidebar-border: #ffffff;
  --sidebar-ring: #ff0000;
}

@theme inline {
  /* Adding font family variables for the new brutalist fonts */
  --font-sans: var(--font-inter);
  --font-serif: var(--font-space-grotesk);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: 0px;
  --radius-md: 0px;
  --radius-lg: 0px;
  --radius-xl: 0px;
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Adding custom neobrutalist utility classes */
@layer utilities {
  .brutalist-shadow {
    box-shadow: 8px 8px 0px #000000;
  }

  .brutalist-shadow-sm {
    box-shadow: 4px 4px 0px #000000;
  }

  .brutalist-shadow-lg {
    box-shadow: 12px 12px 0px #000000;
  }

  .brutalist-border {
    border: 4px solid #000000;
  }

  .brutalist-border-thick {
    border: 6px solid #000000;
  }
}
