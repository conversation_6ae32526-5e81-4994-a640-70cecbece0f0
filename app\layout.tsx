import type React from "react"
import type { <PERSON>ada<PERSON> } from "next"
import { Space_Grotesk, Inter } from "next/font/google"
import "./globals.css"
import ScrollToTop from "@/components/scroll-to-top"

const spaceGrotesk = Space_Grotesk({
  subsets: ["latin"],
  display: "swap",
  variable: "--font-space-grotesk",
  weight: ["400", "500", "600", "700"],
})

const inter = Inter({
  subsets: ["latin"],
  display: "swap",
  variable: "--font-inter",
  weight: ["400", "500", "600"],
})

export const metadata: Metadata = {
  title: "mo.yo - African Software Development Company",
  description:
    "Custom software, co-sourcing, data analytics, product design, and cloud services. Serving Africa and the world.",
  generator: "v0.app",
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en" className={`${spaceGrotesk.variable} ${inter.variable} antialiased`}>
      <body className="font-sans">
        <ScrollToTop />
        {children}
      </body>
    </html>
  )
}
