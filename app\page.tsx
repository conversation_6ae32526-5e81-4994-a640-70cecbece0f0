import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { ExternalLink, Code, Users, BarChart3, Palette, Cloud, ArrowRight, Globe, Zap } from "lucide-react"
import Navigation from "@/components/navigation"
import Link from "next/link"

export default function HomePage() {
  return (
    <div className="min-h-screen bg-background">
      <Navigation />

      <section className="py-24 px-4 relative overflow-hidden">
        <div className="container mx-auto">
          <div className="max-w-5xl">
            <div className="mb-8">
              <div className="inline-block bg-accent text-accent-foreground px-6 py-3 brutalist-border brutalist-shadow-sm mb-6">
                <span className="font-serif font-bold text-lg uppercase tracking-wide">AFRICAN INNOVATION</span>
              </div>
            </div>
            <h2 className="text-7xl md:text-9xl font-serif font-bold text-foreground leading-none mb-8 tracking-tight">
              SOFTWARE
              <br />
              <span className="text-accent">DEVELOPMENT</span>
              <br />
              <span className="relative">
                FROM AFRICA
                <div className="absolute -bottom-2 left-0 w-full h-2 bg-accent"></div>
              </span>
            </h2>
            <p className="text-2xl text-foreground mb-12 max-w-3xl font-medium leading-relaxed">
              We build custom software, provide top tech talent, and deliver data-driven solutions for clients across
              Africa and globally. <span className="text-accent font-bold">NO COMPROMISE.</span>
            </p>
            <div className="flex flex-col sm:flex-row gap-6">
              <Link href="/contact-us">
                <Button
                  size="lg"
                  className="bg-accent hover:bg-accent/90 text-accent-foreground brutalist-border brutalist-shadow font-bold text-lg px-8 py-6 uppercase tracking-wide"
                >
                  START PROJECT
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
              <Link href="/services">
                <Button
                  variant="outline"
                  size="lg"
                  className="brutalist-border brutalist-shadow bg-background hover:bg-muted font-bold text-lg px-8 py-6 uppercase tracking-wide"
                >
                  VIEW SERVICES
                </Button>
              </Link>
            </div>
          </div>
        </div>
        <div className="absolute top-20 right-10 w-20 h-20 bg-foreground brutalist-border rotate-12 hidden lg:block"></div>
        <div className="absolute bottom-20 right-32 w-16 h-16 bg-accent brutalist-border -rotate-12 hidden lg:block"></div>
      </section>

      <section id="services" className="py-24 px-4 bg-muted">
        <div className="container mx-auto">
          <div className="mb-16">
            <h3 className="text-6xl font-serif font-bold text-foreground mb-6 uppercase tracking-tight">
              OUR SERVICES
            </h3>
            <div className="w-32 h-2 bg-accent"></div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Custom Software - Featured card */}
            <Card className="lg:col-span-2 p-8 brutalist-border brutalist-shadow bg-background hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all duration-200 min-h-[400px]">
              <Code className="h-16 w-16 text-accent mb-6" />
              <h4 className="text-3xl font-serif font-bold text-foreground mb-6 uppercase tracking-tight">
                CUSTOM SOFTWARE
              </h4>
              <p className="text-foreground mb-8 text-lg leading-relaxed">
                Enterprise applications, digital platforms, and mobile apps built with cutting-edge technology and
                <span className="text-accent font-bold"> AFRICAN INNOVATION.</span>
              </p>
              <div className="grid grid-cols-2 gap-4">
                <div className="bg-muted p-4 brutalist-border">
                  <div className="font-bold text-foreground">WEB APPS</div>
                </div>
                <div className="bg-muted p-4 brutalist-border">
                  <div className="font-bold text-foreground">MOBILE APPS</div>
                </div>
                <div className="bg-muted p-4 brutalist-border">
                  <div className="font-bold text-foreground">ENTERPRISE</div>
                </div>
                <div className="bg-muted p-4 brutalist-border">
                  <div className="font-bold text-foreground">API DEV</div>
                </div>
              </div>
            </Card>

            {/* Co-Sourcing */}
            <Card className="p-8 brutalist-border brutalist-shadow bg-background hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all duration-200 min-h-[400px]">
              <Users className="h-16 w-16 text-accent mb-6" />
              <h4 className="text-3xl font-serif font-bold text-foreground mb-6 uppercase tracking-tight">
                CO-SOURCING
              </h4>
              <p className="text-foreground text-lg mb-8 leading-relaxed">
                Access top African tech talent and dedicated teams for your projects.
              </p>
              <div className="space-y-4">
                <div className="bg-accent text-accent-foreground p-4 brutalist-border font-bold text-center text-lg">
                  DEVELOPERS
                </div>
                <div className="bg-accent text-accent-foreground p-4 brutalist-border font-bold text-center text-lg">
                  DESIGNERS
                </div>
                <div className="bg-accent text-accent-foreground p-4 brutalist-border font-bold text-center text-lg">
                  TEAMS
                </div>
              </div>
            </Card>

            {/* Data & Analytics */}
            <Card className="p-8 brutalist-border brutalist-shadow bg-background hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all duration-200 min-h-[400px]">
              <BarChart3 className="h-16 w-16 text-accent mb-6" />
              <h4 className="text-3xl font-serif font-bold text-foreground mb-6 uppercase tracking-tight">
                DATA & ANALYTICS
              </h4>
              <p className="text-foreground text-lg mb-8 leading-relaxed">
                Business intelligence, data science, and engineering solutions that drive decisions.
              </p>
              <div className="grid grid-cols-3 gap-3">
                <div className="bg-muted p-4 brutalist-border text-center font-bold">BI</div>
                <div className="bg-muted p-4 brutalist-border text-center font-bold">ML</div>
                <div className="bg-muted p-4 brutalist-border text-center font-bold">ETL</div>
              </div>
            </Card>

            {/* Product Design */}
            <Card className="p-8 brutalist-border brutalist-shadow bg-background hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all duration-200 min-h-[400px]">
              <Palette className="h-16 w-16 text-accent mb-6" />
              <h4 className="text-3xl font-serif font-bold text-foreground mb-6 uppercase tracking-tight">
                PRODUCT DESIGN
              </h4>
              <p className="text-foreground text-lg mb-8 leading-relaxed">
                Strategy, design thinking, and UX that puts users first.
              </p>
              <div className="space-y-4">
                <div className="bg-accent text-accent-foreground p-4 brutalist-border font-bold text-center text-lg">
                  UX RESEARCH
                </div>
                <div className="bg-accent text-accent-foreground p-4 brutalist-border font-bold text-center text-lg">
                  UI DESIGN
                </div>
              </div>
            </Card>

            {/* Cloud Services */}
            <Card className="lg:col-span-2 p-8 brutalist-border brutalist-shadow bg-background hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all duration-200 min-h-[400px]">
              <Cloud className="h-16 w-16 text-accent mb-6" />
              <h4 className="text-3xl font-serif font-bold text-foreground mb-6 uppercase tracking-tight">
                CLOUD SERVICES
              </h4>
              <p className="text-foreground text-lg mb-8 leading-relaxed">
                Complete cloud transformation from assessment to deployment and beyond.
              </p>
              <div className="grid grid-cols-4 gap-4">
                <div className="bg-muted p-4 brutalist-border text-center font-bold">ASSESS</div>
                <div className="bg-muted p-4 brutalist-border text-center font-bold">DESIGN</div>
                <div className="bg-muted p-4 brutalist-border text-center font-bold">BUILD</div>
                <div className="bg-muted p-4 brutalist-border text-center font-bold">SUPPORT</div>
              </div>
            </Card>
          </div>
        </div>
      </section>

      <section id="about" className="py-24 px-4">
        <div className="container mx-auto">
          <div className="grid md:grid-cols-2 gap-16 items-start">
            <div>
              <div className="mb-8">
                <div className="inline-block bg-foreground text-background px-6 py-3 brutalist-border brutalist-shadow-sm mb-6">
                  <span className="font-serif font-bold text-lg uppercase tracking-wide">WHO WE ARE</span>
                </div>
              </div>
              <h3 className="text-5xl font-serif font-bold text-foreground mb-8 uppercase tracking-tight leading-tight">
                BUILT IN AFRICA,
                <br />
                <span className="text-accent">SERVING THE WORLD</span>
              </h3>
              <p className="text-xl text-foreground mb-8 leading-relaxed font-medium">
                mo.yo represents the new generation of African software companies. We combine local insight with global
                standards to deliver <span className="text-accent font-bold">EXCEPTIONAL</span> digital solutions.
              </p>
              <p className="text-foreground mb-10 text-lg leading-relaxed">
                Our team of experienced developers, designers, and strategists work with clients across Africa and
                internationally, bringing fresh perspectives and innovative approaches to every project.
              </p>
              <Button className="bg-accent hover:bg-accent/90 text-accent-foreground brutalist-border brutalist-shadow font-bold text-lg px-8 py-6 uppercase tracking-wide">
                LEARN MORE
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </div>
            <div className="grid grid-cols-2 gap-6">
              <div className="p-8 bg-muted brutalist-border brutalist-shadow hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all duration-200">
                <div className="text-5xl font-serif font-bold text-accent mb-3">50+</div>
                <div className="text-foreground font-bold uppercase tracking-wide">PROJECTS DELIVERED</div>
              </div>
              <div className="p-8 bg-muted brutalist-border brutalist-shadow hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all duration-200">
                <div className="text-5xl font-serif font-bold text-accent mb-3">15+</div>
                <div className="text-foreground font-bold uppercase tracking-wide">COUNTRIES SERVED</div>
              </div>
              <div className="p-8 bg-muted brutalist-border brutalist-shadow hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all duration-200">
                <div className="text-5xl font-serif font-bold text-accent mb-3">5</div>
                <div className="text-foreground font-bold uppercase tracking-wide">YEARS EXPERIENCE</div>
              </div>
              <div className="p-8 bg-muted brutalist-border brutalist-shadow hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all duration-200">
                <div className="text-5xl font-serif font-bold text-accent mb-3">24/7</div>
                <div className="text-foreground font-bold uppercase tracking-wide">SUPPORT AVAILABLE</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section id="clients" className="py-24 px-4 bg-muted">
        <div className="container mx-auto">
          <div className="mb-16">
            <h3 className="text-6xl font-serif font-bold text-foreground mb-6 uppercase tracking-tight">OUR WORK</h3>
            <div className="w-32 h-2 bg-accent"></div>
          </div>

          <div className="grid md:grid-cols-2 gap-8">
            <Card className="p-10 brutalist-border brutalist-shadow bg-background hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-200">
              <div className="mb-6">
                <div className="inline-block bg-accent text-accent-foreground px-4 py-2 brutalist-border font-bold text-sm uppercase tracking-wide mb-4">
                  LAUNDRY SERVICE
                </div>
              </div>
              <h4 className="text-3xl font-serif font-bold text-foreground mb-6 uppercase tracking-tight">
                OUMAETTI NAHSLA LAUNDRY
              </h4>
              <p className="text-foreground mb-8 text-lg leading-relaxed">
                Complete digital transformation for South Africa's leading laundry service, including online booking,
                customer management, and mobile app. <span className="text-accent font-bold">FULL STACK SOLUTION.</span>
              </p>
              <div className="flex items-center gap-3 text-accent hover:text-accent/80 transition-colors group">
                <ExternalLink className="h-6 w-6 group-hover:translate-x-1 group-hover:-translate-y-1 transition-transform" />
                <a
                  href="https://oumaettinahslaundry.co.za/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="font-bold text-lg uppercase tracking-wide"
                >
                  VISIT SITE
                </a>
              </div>
            </Card>

            <Card className="p-10 brutalist-border brutalist-shadow bg-background hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-200">
              <div className="mb-6">
                <div className="inline-block bg-accent text-accent-foreground px-4 py-2 brutalist-border font-bold text-sm uppercase tracking-wide mb-4">
                  INSURANCE BROKER
                </div>
              </div>
              <h4 className="text-3xl font-serif font-bold text-foreground mb-6 uppercase tracking-tight">
                GC BROKERS
              </h4>
              <p className="text-foreground mb-8 text-lg leading-relaxed">
                Modern web platform for insurance brokerage services, featuring client portals, policy management, and
                automated workflows. <span className="text-accent font-bold">ENTERPRISE GRADE.</span>
              </p>
              <div className="flex items-center gap-3 text-accent hover:text-accent/80 transition-colors group">
                <ExternalLink className="h-6 w-6 group-hover:translate-x-1 group-hover:-translate-y-1 transition-transform" />
                <a
                  href="https://gcbrokers.co.za/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="font-bold text-lg uppercase tracking-wide"
                >
                  VISIT SITE
                </a>
              </div>
            </Card>
          </div>
        </div>
      </section>

      <section id="contact" className="py-24 px-4 relative overflow-hidden">
        <div className="container mx-auto text-center">
          <div className="mb-8">
            <div className="inline-block bg-foreground text-background px-6 py-3 brutalist-border brutalist-shadow-sm mb-6">
              <span className="font-serif font-bold text-lg uppercase tracking-wide">GET STARTED</span>
            </div>
          </div>
          <h3 className="text-6xl font-serif font-bold text-foreground mb-8 uppercase tracking-tight">
            READY TO START?
          </h3>
          <p className="text-2xl text-foreground mb-12 max-w-4xl mx-auto leading-relaxed font-medium">
            Let's discuss your project and see how mo.yo can help bring your ideas to life with
            <span className="text-accent font-bold"> AFRICAN INNOVATION</span> and global standards.
          </p>
          <div className="flex flex-col sm:flex-row gap-6 justify-center">
            <Link href="/contact-us">
              <Button
                size="lg"
                className="bg-accent hover:bg-accent/90 text-accent-foreground brutalist-border brutalist-shadow font-bold text-xl px-12 py-8 uppercase tracking-wide"
              >
                GET IN TOUCH
                <Zap className="ml-3 h-6 w-6" />
              </Button>
            </Link>
            <Link href="/contact-us">
              <Button
                variant="outline"
                size="lg"
                className="brutalist-border brutalist-shadow bg-background hover:bg-muted font-bold text-xl px-12 py-8 uppercase tracking-wide"
              >
                SCHEDULE CALL
                <Globe className="ml-3 h-6 w-6" />
              </Button>
            </Link>
          </div>
        </div>
        <div className="absolute top-10 left-10 w-24 h-24 bg-accent brutalist-border rotate-45 hidden lg:block"></div>
        <div className="absolute bottom-10 right-10 w-20 h-20 bg-foreground brutalist-border -rotate-12 hidden lg:block"></div>
      </section>

      <footer className="brutalist-border-thick border-t-0 border-l-0 border-r-0 bg-muted py-16 px-4">
        <div className="container mx-auto">
          <div className="grid md:grid-cols-4 gap-12">
            <div>
              <div className="flex items-center space-x-3 mb-6">
                <div className="w-10 h-10 bg-accent brutalist-border brutalist-shadow-sm"></div>
                <h4 className="text-2xl font-serif font-bold text-foreground">mo.yo</h4>
              </div>
              <p className="text-foreground font-medium leading-relaxed">
                African software development company serving clients globally with
                <span className="text-accent font-bold"> UNCOMPROMISING QUALITY.</span>
              </p>
            </div>
            <div>
              <h5 className="font-serif font-bold text-foreground mb-6 text-xl uppercase tracking-wide">SERVICES</h5>
              <ul className="space-y-3 text-foreground font-medium">
                <li className="hover:text-accent transition-colors cursor-pointer">CUSTOM SOFTWARE</li>
                <li className="hover:text-accent transition-colors cursor-pointer">CO-SOURCING</li>
                <li className="hover:text-accent transition-colors cursor-pointer">DATA & ANALYTICS</li>
                <li className="hover:text-accent transition-colors cursor-pointer">PRODUCT DESIGN</li>
                <li className="hover:text-accent transition-colors cursor-pointer">CLOUD SERVICES</li>
              </ul>
            </div>
            <div>
              <h5 className="font-serif font-bold text-foreground mb-6 text-xl uppercase tracking-wide">COMPANY</h5>
              <ul className="space-y-3 text-foreground font-medium">
                <li className="hover:text-accent transition-colors cursor-pointer">ABOUT US</li>
                <li className="hover:text-accent transition-colors cursor-pointer">OUR WORK</li>
                <li className="hover:text-accent transition-colors cursor-pointer">CAREERS</li>
                <li className="hover:text-accent transition-colors cursor-pointer">CONTACT</li>
              </ul>
            </div>
            <div>
              <h5 className="font-serif font-bold text-foreground mb-6 text-xl uppercase tracking-wide">CONNECT</h5>
              <div className="space-y-4">
                <div className="bg-background p-4 brutalist-border">
                  <p className="text-foreground font-bold"><EMAIL></p>
                </div>
                <div className="bg-background p-4 brutalist-border">
                  <p className="text-foreground font-bold">wearemoyo.dev</p>
                </div>
              </div>
            </div>
          </div>
          <div className="brutalist-border-thick border-t-0 border-l-0 border-r-0 mt-12 pt-8 text-center">
            <p className="text-foreground font-bold text-lg uppercase tracking-wide">
              © 2024 mo.yo. BUILT WITH <span className="text-accent">AFRICAN INNOVATION.</span>
            </p>
          </div>
        </div>
      </footer>
    </div>
  )
}
